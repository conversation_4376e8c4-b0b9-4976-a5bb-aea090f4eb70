/* Category Navigation Styles */

/* Navigation */
.category-nav {
  background: linear-gradient(135deg, #0079FF 0%, #004AAD 100%);
  width: 100%;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 4px 20px rgba(0, 121, 255, 0.3);
}

.category-nav-container {
  width: 100%;
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: none;
  margin: 0;
}

.nav-logo {
  font-size: 1.5rem;
  font-weight: 300;
  text-shadow: none;
}

.nav-logo-aurea {
  color: #ffffff;
}

.nav-logo-voice {
  color: #fbbf24;
}

/* Navigation buttons */
.nav-buttons {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.nav-button {
  background: rgba(255, 255, 255, 0.2);
  color: #ffffff;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.nav-button:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
}

.nav-button:active {
  transform: translateY(0);
}

/* Responsive navigation */
@media (max-width: 768px) {
  .category-nav-container {
    padding: 0.75rem 1rem;
  }
  
  .nav-logo {
    font-size: 1.25rem;
  }
  
  .nav-buttons {
    gap: 0.5rem;
  }
  
  .nav-button {
    padding: 0.375rem 0.75rem;
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .category-nav-container {
    padding: 0.5rem 0.75rem;
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .nav-logo {
    font-size: 1.125rem;
  }
  
  .nav-buttons {
    width: 100%;
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .nav-button {
    flex: 1;
    min-width: 80px;
    text-align: center;
    padding: 0.5rem 0.5rem;
    font-size: 0.75rem;
  }
}
