/* AureaVoice Category Sidebar Styles */

/* Sidebar Container */
.category-sidebar {
  background: #ffffff;
  border-radius: 0.75rem;
  border: 1px solid rgba(0, 0, 0, 0.1);
  height: fit-content;
  position: sticky;
  top: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.sidebar-content {
  padding: 1.5rem 5rem 1.5rem 5rem;
}

.sidebar-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 1.5rem;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid #e2e8f0;
}

/* Practice Items */
.practice-items {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.practice-item {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  padding: 1rem;
  transition: all 0.2s ease;
}

.practice-item:hover {
  background: #ffffff;
  border-color: #0079FF;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 121, 255, 0.1);
}

.practice-header {
  margin-bottom: 0.75rem;
}

.practice-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  line-height: 1.4;
}

.practice-description {
  color: #64748b;
  font-size: 0.875rem;
  line-height: 1.5;
  margin-bottom: 1rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.practice-button {
  background: #0079FF;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 600;
  width: 100%;
  transition: all 0.2s ease;
}

.practice-button:hover {
  background: #004AAD;
  transform: translateY(-1px);
}

.practice-button:active {
  transform: translateY(0);
}

/* Responsive Design for Sidebar */
@media (max-width: 1024px) {
  .category-layout {
    grid-template-columns: 1fr;
    grid-template-areas: 
      "main"
      "sidebar";
    gap: 1.5rem;
  }
  
  .category-sidebar {
    position: static;
    top: auto;
  }
  
  .practice-items {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
  }
}

@media (max-width: 768px) {
  .sidebar-content {
    padding: 1rem;
  }
  
  .practice-items {
    grid-template-columns: 1fr;
  }
  

}
