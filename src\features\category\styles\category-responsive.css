/* AureaVoice Category Responsive Styles */

/* Tablet Styles */
@media (max-width: 1024px) {
  .banner-content {
    grid-template-columns: 1fr;
    text-align: center;
    gap: 1.5rem;
  }
  
  .banner-title {
    font-size: 2rem;
  }
  
  .banner-subtitle {
    font-size: 1rem;
  }
  
  .material-article {
    padding: 1.5rem;
  }
  
  .pronunciation-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }

  .mistakes-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }

  .more-materials-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }
}

/* Mobile Styles */
@media (max-width: 768px) {
  .category-banner {
    padding: 2rem 1rem;
    margin-bottom: 1.5rem;
  }
  
  .banner-title {
    font-size: 1.75rem;
  }
  
  .banner-subtitle {
    font-size: 0.875rem;
  }
  
  .banner-img {
    height: 150px;
  }
  
  .category-layout {
    padding: 0 0.75rem 1.5rem;
    gap: 1rem;
  }
  
  .section-title {
    font-size: 1.25rem;
    margin-bottom: 1rem;
  }
  
  .material-section,
  .pronunciation-section,
  .mistakes-section,
  .more-materials-section {
    margin-bottom: 2rem;
  }

  .material-article {
    padding: 1.25rem;
  }

  .material-title {
    font-size: 1.5rem;
  }

  .material-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .material-content h2 {
    font-size: 1.25rem;
  }

  .material-content h3 {
    font-size: 1.125rem;
  }
  
  .pronunciation-grid,
  .tips-grid,
  .mistakes-grid,
  .more-materials-grid {
    grid-template-columns: 1fr;
  }
  
  .pronunciation-card {
    padding: 1rem;
  }
  
  .word-text {
    font-size: 1.125rem;
  }
  
  .pronunciation-controls {
    flex-direction: column;
    gap: 0.75rem;
    align-items: stretch;
  }
  
  .play-button {
    justify-content: center;
  }
}

/* Small Mobile Styles */
@media (max-width: 480px) {
  .category-banner {
    padding: 1.5rem 0.75rem;
  }
  
  .banner-title {
    font-size: 1.5rem;
  }
  
  .banner-img {
    height: 120px;
  }
  
  .category-layout {
    padding: 0 0.5rem 1rem;
  }
  
  .material-article,
  .pronunciation-card {
    padding: 1rem;
  }

  .material-title {
    font-size: 1.25rem;
  }

  .material-content {
    font-size: 0.875rem;
  }

  .material-content h2 {
    font-size: 1.125rem;
  }

  .material-content h3 {
    font-size: 1rem;
  }
  
  .word-text {
    font-size: 1rem;
  }
  
  .phonetic-text {
    font-size: 0.75rem;
  }
  
  .play-button {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
  }
  
  .difficulty-badge {
    font-size: 0.625rem;
    padding: 0.125rem 0.5rem;
  }
}

/* Print Styles */
@media print {
  .category-banner {
    background: #f8fafc !important;
    color: #1e293b !important;
    -webkit-print-color-adjust: exact;
  }
  
  .banner-img {
    display: none;
  }
  
  .play-button,
  .practice-button {
    display: none;
  }
  
  .category-layout {
    grid-template-columns: 1fr;
    grid-template-areas: 
      "main"
      "sidebar";
  }
  
  .material-article,
  .pronunciation-card,
  .practice-item {
    break-inside: avoid;
    box-shadow: none !important;
    border: 1px solid #e2e8f0 !important;
  }
}
