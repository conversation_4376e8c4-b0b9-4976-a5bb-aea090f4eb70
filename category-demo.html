<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo Page Kategori - AureaVoice</title>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Import Category Styles -->
    <link rel="stylesheet" href="src/features/category/styles/index.css">
    
    <style>
        /* Basic reset and body styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Poppins', sans-serif;
            background: #f8fafc;
            line-height: 1.6;
        }
        
        #app {
            min-height: 100vh;
        }
        
        /* Demo navigation */
        .demo-nav {
            background: #1e293b;
            color: white;
            padding: 1rem;
            text-align: center;
            margin-bottom: 0;
        }
        
        .demo-nav h1 {
            font-size: 1.5rem;
            font-weight: 600;
        }
        
        .demo-nav p {
            opacity: 0.8;
            margin-top: 0.5rem;
        }
    </style>
</head>
<body>
    <!-- Demo Navigation -->
    <div class="demo-nav">
        <h1>Demo Page Kategori AureaVoice</h1>
        <p>Halaman ini menampilkan page kategori dengan navbar, tips practice, kesalahan umum, dan materi tambahan</p>
    </div>
    
    <!-- App Container -->
    <div id="app"></div>

    <script type="module">
        // Import the category components
        import { CategoryModel, CategoryPresenter } from './src/features/category/index.js';
        
        // Initialize the category page
        document.addEventListener('DOMContentLoaded', () => {
            // Create model and presenter
            const categoryModel = new CategoryModel();
            const categoryPresenter = new CategoryPresenter(categoryModel);
            
            // Initialize with default category (pronunciation)
            categoryPresenter.init('pronunciation');
            
            console.log('Category page demo initialized successfully!');
        });
    </script>
</body>
</html>
